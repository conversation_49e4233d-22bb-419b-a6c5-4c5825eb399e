package vn.khanhduc.chatmessage.websocket;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.messaging.simp.config.ChannelRegistration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;

@Configuration
@RequiredArgsConstructor
@EnableWebSocketMessageBroker
public class WebsocketConfiguration implements WebSocketMessageBrokerConfigurer {

    private final ChannelInterceptorConfiguration channelInterceptor;

    @Override
    public void registerStompEndpoints(@NonNull StompEndpointRegistry registry) {
        registry.addEndpoint("/ws")
                .addInterceptors(new WebSocketHandshakeInterceptor())
                .setAllowedOrigins("http://localhost:3000")
                .setAllowedOriginPatterns("http://localhost:*")
                .withSockJS();

        registry.addEndpoint("/ws-mobile")
                .addInterceptors(new WebSocketHandshakeInterceptor())
                .setAllowedOrigins("*")
                .setAllowedOriginPatterns("*");
    }

    @Override
    public void configureMessageBroker(@NonNull MessageBrokerRegistry registry) {
        registry.enableSimpleBroker("/topic", "/queue");

        registry.setApplicationDestinationPrefixes("/app");
        registry.setUserDestinationPrefix("/user");
    }

    @Override
    public void configureClientInboundChannel(@NonNull ChannelRegistration registration) {
        registration.interceptors(channelInterceptor);
        registration.taskExecutor()
                .corePoolSize(12)
                .maxPoolSize(18)
                .keepAliveSeconds(60)
                .queueCapacity(800);
    }

    @Override
    public void configureClientOutboundChannel(@NonNull ChannelRegistration registration) {
        registration.taskExecutor()
                .corePoolSize(6)
                .maxPoolSize(12)
                .queueCapacity(1000)
                .keepAliveSeconds(60);
    }

}
