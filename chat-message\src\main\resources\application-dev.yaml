spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************
    username: root
    password: 123456

  jpa:
    hibernate:
      ddl-auto: update

  data:
    redis:
      host: localhost
      port: 6379
      database: 0

  kafka:
    bootstrap-servers: localhost:9094

  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 102MB

aws:
  s3:
    region: ap-southeast-1
    bucket-name: chat-bucket-files
    max-file-size: ******** # 100MB
    access-key: ********************
    secret-key: CWKHPB4aHY202ou60USvPrANCEYEjeArRUoOMA8y

twilio:
  account-id: **********************************
  auth-token: 4befadffb8d11ec609021c9244409683
  secret: 8gLWHakD3guqte8xOSdVr7PpKNPSsBUD
  sid: **********************************

jwt:
  secret-key: LlXbOuam4UBLZOg9uylp4Bp4IzL0p9Vb/leiOexBiXBZnHGkzH/lVxamrIjm/pv6rDDsqLmFhlfTC2rGzLf603+SkhUFr4OTh/rAcCPfx1Y=