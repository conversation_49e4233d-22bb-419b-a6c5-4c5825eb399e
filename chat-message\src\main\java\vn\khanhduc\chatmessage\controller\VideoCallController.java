package vn.khanhduc.chatmessage.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.video.VideoCallJoinRequest;
import vn.khanhduc.chatmessage.dto.video.VideoCallRoomResponse;
import vn.khanhduc.chatmessage.dto.video.VideoCallTokenRequest;
import vn.khanhduc.chatmessage.dto.video.VideoCallTokenResponse;
import vn.khanhduc.chatmessage.service.VideoCallService;
import vn.khanhduc.chatmessage.service.TwilioVideoService;

@RestController
@RequestMapping("/video")
@RequiredArgsConstructor
@Slf4j
public class VideoCallController {

    private final VideoCallService videoCallService;
    private final TwilioVideoService twilioVideoService;

    @PostMapping("/token")
    public ApiResponse<VideoCallTokenResponse> getTwilioAccessToken(
            @RequestBody VideoCallTokenRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("Getting Twilio access token for user: {} in conversation: {}", userId, request.getConversationId());

            String token = twilioVideoService.generateAccessToken(userId, request.getConversationId());

            VideoCallTokenResponse response = VideoCallTokenResponse.builder()
                    .token(token)
                    .roomName(request.getConversationId())
                    .identity(userId)
                    .build();

            return ApiResponse.<VideoCallTokenResponse>builder()
                    .code(200)
                    .message("Success")
                    .data(response)
                    .build();
        } catch (Exception e) {
            log.error("Error getting Twilio access token", e);
            return ApiResponse.<VideoCallTokenResponse>builder()
                    .code(400)
                    .message("Failed to get access token: " + e.getMessage())
                    .build();
        }
    }

    @PostMapping("/join")
    public ApiResponse<VideoCallRoomResponse> joinRoom(
            @RequestBody VideoCallJoinRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} joining video call room: {}", userId, request.getConversationId());

            VideoCallRoomResponse response = videoCallService.joinRoom(
                    request.getConversationId(),
                    userId,
                    request
            );

            return ApiResponse.<VideoCallRoomResponse>builder()
                    .code(200)
                    .message("Success")
                    .data(response)
                    .build();
        } catch (Exception e) {
            log.error("Error joining video call room", e);
            return ApiResponse.<VideoCallRoomResponse>builder()
                    .code(400)
                    .message("Failed to join room: " + e.getMessage())
                    .build();
        }
    }

    @PostMapping("/leave")
    public ApiResponse<Void> leaveRoom(
            @RequestBody VideoCallLeaveRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} leaving video call room: {}", userId, request.getConversationId());

            videoCallService.leaveRoom(request.getConversationId(), userId);

            return ApiResponse.<Void>builder()
                    .code(200)
                    .message("Success")
                    .build();
        } catch (Exception e) {
            log.error("Error leaving video call room", e);
            return ApiResponse.<Void>builder()
                    .code(400)
                    .message("Failed to leave room: " + e.getMessage())
                    .build();
        }
    }

    @GetMapping("/{conversationId}/participants")
    public ApiResponse<VideoCallParticipantsResponse> getRoomParticipants(
            @PathVariable String conversationId,
            Authentication authentication) {
        try {
            log.info("Getting participants for video call room: {}", conversationId);

            VideoCallParticipantsResponse response = videoCallService.getRoomParticipants(conversationId);

            return ApiResponse.<VideoCallParticipantsResponse>builder()
                    .code(200)
                    .message("Success")
                    .data(response)
                    .build();
        } catch (Exception e) {
            log.error("Error getting room participants", e);
            return ApiResponse.<VideoCallParticipantsResponse>builder()
                    .code(400)
                    .message("Failed to get participants: " + e.getMessage())
                    .build();
        }
    }

    @PostMapping("/invite")
    public ApiResponse<Void> inviteToCall(
            @RequestBody VideoCallInviteRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} inviting participants to video call: {}", userId, request.getConversationId());

            videoCallService.inviteToCall(
                    request.getConversationId(),
                    userId,
                    request.getParticipantIds()
            );

            return ApiResponse.<Void>builder()
                    .code(200)
                    .message("Success")
                    .build();
        } catch (Exception e) {
            log.error("Error inviting to video call", e);
            return ApiResponse.<Void>builder()
                    .code(400)
                    .message("Failed to invite to call: " + e.getMessage())
                    .build();
        }
    }

    @PostMapping("/end")
    public ApiResponse<Void> endCall(
            @RequestBody VideoCallEndRequest request,
            Authentication authentication) {
        try {
            String userId = authentication.getName();
            log.info("User {} ending video call: {}", userId, request.getConversationId());

            videoCallService.endCall(request.getConversationId(), userId);

            return ApiResponse.<Void>builder()
                    .code(200)
                    .message("Success")
                    .build();
        } catch (Exception e) {
            log.error("Error ending video call", e);
            return ApiResponse.<Void>builder()
                    .code(400)
                    .message("Failed to end call: " + e.getMessage())
                    .build();
        }
    }
}
