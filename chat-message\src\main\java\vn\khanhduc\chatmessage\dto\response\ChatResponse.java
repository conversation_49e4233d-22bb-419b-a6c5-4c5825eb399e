package vn.khanhduc.chatmessage.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import vn.khanhduc.chatmessage.dto.request.MediaAttachment;
import vn.khanhduc.chatmessage.enums.MessageStatus;
import vn.khanhduc.chatmessage.enums.MessageType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ChatResponse {
    private String id;
    private String tempId;
    private String conversationId;
    private boolean me;
    private String username;
    private String content;
    private MessageStatus status;
    private boolean isRead;
    private List<MediaAttachment> mediaAttachments;
    private MessageType messageType;
    private LocalDateTime createdAt;

}
