package vn.khanhduc.chatmessage.controller;

import vn.khanhduc.chatmessage.dto.request.ConversationCreationRequest;
import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.response.ConversationCreationResponse;
import vn.khanhduc.chatmessage.service.ConversationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/conversations")
public class ConversationController {

    private final ConversationService conversationService;

    @PostMapping
    ApiResponse<ConversationCreationResponse> createConversation(@RequestBody ConversationCreationRequest request) {
        return ApiResponse.<ConversationCreationResponse>builder()
                .code(HttpStatus.CREATED.value())
                .data(conversationService.create(request))
                .build();
    }

    @GetMapping
    ApiResponse<List<ConversationCreationResponse>> getAllMyConversation() {
        return ApiResponse.<List<ConversationCreationResponse>>builder()
                .code(HttpStatus.OK.value())
                .data(conversationService.myConversation())
                .build();
    }

    @DeleteMapping("/{conversationId}")
    ApiResponse<Void> deleteConversation(@PathVariable String conversationId) {
        conversationService.deleteConversation(conversationId);
        return ApiResponse.<Void>builder()
                .code(HttpStatus.OK.value())
                .message("Conversation deleted")
                .build();
    }

}
