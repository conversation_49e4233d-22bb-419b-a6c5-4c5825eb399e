package vn.khanhduc.chatmessage.repository;

import vn.khanhduc.chatmessage.entity.VideoCall;
import vn.khanhduc.chatmessage.enums.CallStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface VideoCallRepository extends JpaRepository<VideoCall, String> {

    List<VideoCall> findByConversationIdOrderByCreatedAtDesc(String conversationId);

    @Query("SELECT v FROM VideoCall v WHERE v.conversationId = :conversationId AND v.status = :status")
    Optional<VideoCall> findByConversationIdAndStatus(@Param("conversationId") String conversationId, 
                                                      @Param("status") CallStatus status);

    @Query("SELECT v FROM VideoCall v WHERE (v.callerId = :userId OR v.receiverId = :userId) AND v.status IN :statuses")
    List<VideoCall> findByUserIdAndStatusIn(@Param("userId") String userId, @Param("statuses") List<CallStatus> statuses);

    @Query("SELECT COUNT(v) FROM VideoCall v WHERE v.conversationId = :conversationId")
    Long countByConversationId(@Param("conversationId") String conversationId);

    @Query("SELECT SUM(v.durationSeconds) FROM VideoCall v WHERE v.conversationId = :conversationId AND v.durationSeconds IS NOT NULL")
    Long sumDurationByConversationId(@Param("conversationId") String conversationId);
}
