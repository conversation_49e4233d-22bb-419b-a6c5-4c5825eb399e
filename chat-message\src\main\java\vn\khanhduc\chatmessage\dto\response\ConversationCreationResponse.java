package vn.khanhduc.chatmessage.dto.response;

import vn.khanhduc.chatmessage.enums.ConversationType;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
public class ConversationCreationResponse {
    private String id;
    private ConversationType conversationType;
    private String participantHash;
    private String conversationAvatar;
    private String conversationName;
    private List<ParticipantInfoDetailResponse> participantInfo;
    private LocalDateTime createdAt;
}
