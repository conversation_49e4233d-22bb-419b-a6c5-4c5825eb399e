package vn.khanhduc.chatmessage.exception;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;

@Getter
@RequiredArgsConstructor
public enum ErrorCode {

    INTERNAL_SERVER(500, "The system is under maintenance, please try again later.", HttpStatus.INTERNAL_SERVER_ERROR),
    UN_AUTHENTICATED(401, "Unauthenticated", HttpStatus.UNAUTHORIZED),
    ACCESS_DENIED(403, "Access denied", HttpStatus.FORBIDDEN),

    USER_NOT_FOUND(404, "User not existed", HttpStatus.NOT_FOUND),
    USER_EXISTED(400, "User already exists, please choose another email", HttpStatus.BAD_REQUEST),

    CONVERSATION_NOT_FOUND(404, "Conversation not found", HttpStatus.NOT_FOUND),
    CONVERSATION_NAME_REQUIRED(400, "Conversation name is required", HttpStatus.BAD_REQUEST),
    PRIVATE_CONVERSATION_MAX_TWO_PARTICIPANTS(400, "Private conversation max two participants", HttpStatus.BAD_REQUEST),
    GROUP_CONVERSATION_MINIMUM_THREE_PARTICIPANTS(400, "Group conversation minimum three participants", HttpStatus.BAD_REQUEST),

    MESSAGE_NOT_PART_OF_STORY(400, "Message does not belong to conversation", HttpStatus.BAD_REQUEST),
    MESSAGE_NOT_FOUND(404, "Message not found", HttpStatus.NOT_FOUND),
    MEDIA_URL_NOT_BLANK(400, "Media URL is required for non-text messages", HttpStatus.BAD_REQUEST),

    PARTICIPANT_INVALID(400, "One or more participant IDs are invalid", HttpStatus.BAD_REQUEST),

    // Video Call related errors
    CALL_NOT_FOUND(404, "Video call not found", HttpStatus.NOT_FOUND),
    CALL_ALREADY_IN_PROGRESS(400, "There is already an active call in this conversation", HttpStatus.BAD_REQUEST),
    CALL_NOT_AVAILABLE(400, "Call is not available for this action", HttpStatus.BAD_REQUEST),
    RECEIVER_NOT_FOUND(404, "Receiver not found in conversation", HttpStatus.NOT_FOUND)
    ;
    private final int code;
    private final String message;
    private final HttpStatus httpStatus;
}
