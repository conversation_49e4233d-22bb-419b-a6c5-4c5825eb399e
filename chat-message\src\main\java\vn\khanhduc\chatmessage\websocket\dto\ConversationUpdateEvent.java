package vn.khanhduc.chatmessage.websocket.dto;

import lombok.*;
import vn.khanhduc.chatmessage.dto.response.ConversationCreationResponse;
import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ConversationUpdateEvent implements Serializable {

    private String type;
    private ConversationCreationResponse conversation;
    private String userId;
    private Long timestamp;

    public static ConversationUpdateEvent conversationCreated(ConversationCreationResponse conversation, String userId) {
        return ConversationUpdateEvent.builder()
                .type("CONVERSATION_CREATED")
                .conversation(conversation)
                .userId(userId)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    public static ConversationUpdateEvent conversationUpdated(ConversationCreationResponse conversation, String userId) {
        return ConversationUpdateEvent.builder()
                .type("CONVERSATION_UPDATED")
                .conversation(conversation)
                .userId(userId)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    public static ConversationUpdateEvent conversationDeleted(String conversationId, String userId) {
        return ConversationUpdateEvent.builder()
                .type("CONVERSATION_DELETED")
                .conversation(ConversationCreationResponse.builder().id(conversationId).build())
                .userId(userId)
                .timestamp(System.currentTimeMillis())
                .build();
    }

}
