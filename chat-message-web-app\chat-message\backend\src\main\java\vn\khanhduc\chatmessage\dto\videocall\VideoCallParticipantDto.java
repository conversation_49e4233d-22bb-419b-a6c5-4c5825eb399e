package vn.khanhduc.chatmessage.dto.videocall;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class VideoCallParticipantDto {
    private String id;
    private String username;
    private String avatar;
    private boolean isLocal;
    private boolean isMuted;
    private boolean isVideoEnabled;
    private String status; // "connected", "connecting", "disconnected"
}
