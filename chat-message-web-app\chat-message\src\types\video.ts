export interface VideoCallParticipant {
    id: string;
    username: string;
    avatar?: string;
    isLocal: boolean;
    isMuted: boolean;
    isVideoEnabled: boolean;
}

export interface VideoCallRoom {
    roomId: string;
    roomName: string;
    participants: VideoCallParticipant[];
    status: 'connecting' | 'connected' | 'disconnected' | 'failed';
    startTime?: Date;
    endTime?: Date;
}

export interface TwilioAccessToken {
    token: string;
    roomName: string;
    identity: string;
}

export interface VideoCallInvitation {
    id: string;
    conversationId: string;
    roomName: string;
    inviterId: string;
    inviterUsername: string;
    invitedParticipants: string[];
    status: 'pending' | 'accepted' | 'declined' | 'expired';
    createdAt: string;
    expiresAt: string;
}

export interface VideoCallEvent {
    type: 'CALL_INVITATION' | 'CALL_STARTED' | 'CALL_ENDED' | 'PARTICIPANT_JOINED' | 'PARTICIPANT_LEFT';
    data: any;
    timestamp: number;
}

export interface VideoCallSettings {
    video: boolean;
    audio: boolean;
    screenShare: boolean;
}
