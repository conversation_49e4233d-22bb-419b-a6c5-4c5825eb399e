package vn.khanhduc.chatmessage.dto.response;

import vn.khanhduc.chatmessage.enums.CallStatus;
import vn.khanhduc.chatmessage.enums.CallType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoCallResponse {
    private String callId;
    private String conversationId;
    private String callerId;
    private String receiverId;
    private String zegoRoomId;
    private CallType callType;
    private CallStatus status;
    private LocalDateTime startedAt;
    private LocalDateTime endedAt;
    private Integer durationSeconds;
    private LocalDateTime createdAt;
    
    // ZegoCloud specific fields
    private String token;
    private RoomConfig roomConfig;
    
    @Getter
    @Setter
    @Builder
    @JsonInclude(JsonInclude.Include.NON_NULL)
    public static class RoomConfig {
        private String scenario;
        private Integer maxUsers;
        private Boolean enableVideo;
        private Boolean enableAudio;
        private Boolean enableScreenSharing;
        private Boolean enableChat;
        private Boolean enableRecording;
    }
}
