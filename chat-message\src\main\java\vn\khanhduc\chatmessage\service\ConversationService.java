package vn.khanhduc.chatmessage.service;

import org.springframework.messaging.simp.SimpMessagingTemplate;
import vn.khanhduc.chatmessage.dto.request.ConversationCreationRequest;
import vn.khanhduc.chatmessage.dto.response.ConversationCreationResponse;
import vn.khanhduc.chatmessage.entity.Conversation;
import vn.khanhduc.chatmessage.entity.ParticipantInfo;
import vn.khanhduc.chatmessage.entity.User;
import vn.khanhduc.chatmessage.enums.ConversationType;
import vn.khanhduc.chatmessage.exception.AppException;
import vn.khanhduc.chatmessage.exception.ErrorCode;
import vn.khanhduc.chatmessage.mapper.ConversationMapper;
import vn.khanhduc.chatmessage.repository.ConversationRepository;
import vn.khanhduc.chatmessage.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import vn.khanhduc.chatmessage.websocket.dto.ConversationUpdateEvent;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "CONVERSATION-SERVICE")
public class ConversationService {

    private final ConversationRepository conversationRepository;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate simpMessagingTemplate;

    public ConversationCreationResponse create(ConversationCreationRequest request) {

        String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();

        User sender = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        List<String> participantIds = request.getParticipantIds();

        if(!participantIds.contains(sender.getId())) {
            participantIds.add(sender.getId());
        }

        List<User> participants = userRepository.findAllById(participantIds);

        if(participants.size() != participantIds.size()) {
            throw new AppException(ErrorCode.PARTICIPANT_INVALID);
        }

        String participantHash = participantIds.size() > 1
                ? participantIds.stream()
                    .distinct()
                    .sorted().collect(Collectors.joining("_"))
                : participantIds.getFirst();

        // PRIVATE
        if(request.getConversationType() == ConversationType.PRIVATE) {
            if (participantIds.size() > 2) {
                throw new AppException(ErrorCode.PRIVATE_CONVERSATION_MAX_TWO_PARTICIPANTS);
            }
            Optional<Conversation> conversation = conversationRepository.findByParticipantHash(participantHash);
            if(conversation.isPresent()) {
                log.info("Conversation existed");
                return ConversationMapper.mapToConversationResponse(conversation.get(), userId);
            }
        }

        // GROUP OR NEW PRIVATE CONVERSATION
        if (request.getConversationType() == ConversationType.GROUP) {
            if (request.getConversationName() == null || request.getConversationName().trim().isEmpty()) {
                throw new AppException(ErrorCode.CONVERSATION_NAME_REQUIRED);
            }
            if (participantIds.size() < 3) {
                throw new AppException(ErrorCode.GROUP_CONVERSATION_MINIMUM_THREE_PARTICIPANTS);
            }
        }

        List<ParticipantInfo> participantInfos = participants.stream()
                .map(user -> ParticipantInfo.builder()
                        .user(user)
                        .conversation(null)
                        .joinedAt(LocalDateTime.now())
                        .build())
                .toList();

        Conversation conversation = Conversation.builder()
                .name(request.getConversationName())
                .conversationType(request.getConversationType())
                .participantHash(request.getConversationType() == ConversationType.PRIVATE ? participantHash : null)
                .participants(participantInfos)
                .conversationAvatar(request.getConversationType() == ConversationType.GROUP
                        && request.getConversationAvatar() != null ? request.getConversationAvatar() : null)
                .lastMessageAt(LocalDateTime.now())
                .createdAt(LocalDateTime.now())
                .build();

        participantInfos.forEach(participantInfo ->
                participantInfo.setConversation(conversation));

        conversationRepository.save(conversation);

        var conversationCreationResponse = ConversationMapper.mapToConversationResponse(conversation, userId);

        ConversationUpdateEvent event = ConversationUpdateEvent.conversationCreated(
                conversationCreationResponse,
                userId
        );

        participantIds.forEach(participantId ->
                simpMessagingTemplate.convertAndSendToUser(
                        participantId,
                "/queue/conversation-updates",
                event
                )
        );

        return conversationCreationResponse;
    }

    public List<ConversationCreationResponse> myConversation() {
        String userId = SecurityContextHolder.getContext()
                .getAuthentication().getName();

        User currentUser = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        List<Conversation> conversations = conversationRepository.findByParticipantsUserId(currentUser.getId());

        return conversations.stream()
                .map(conversation -> ConversationMapper.mapToConversationResponse(conversation, userId))
                .toList();
    }

    public void deleteConversation(String id) {
        var conversation = conversationRepository.findById(id)
                .orElseThrow(() -> new AppException(ErrorCode.CONVERSATION_NOT_FOUND));
        conversationRepository.delete(conversation);
        log.info("Conversation deleted");
    }

}
