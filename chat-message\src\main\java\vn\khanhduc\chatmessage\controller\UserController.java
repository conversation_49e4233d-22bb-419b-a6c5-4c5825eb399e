package vn.khanhduc.chatmessage.controller;

import vn.khanhduc.chatmessage.dto.request.RegisterFcmTokenRequest;
import vn.khanhduc.chatmessage.dto.request.UserCreationRequest;
import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.response.ParticipantInfoDetailResponse;
import vn.khanhduc.chatmessage.dto.response.UserCreationResponse;
import vn.khanhduc.chatmessage.dto.response.UserDetailResponse;
import vn.khanhduc.chatmessage.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/users")
public class UserController {

    private final UserService userService;

    @PostMapping
    ApiResponse<UserCreationResponse> registry(@RequestBody UserCreationRequest request) {
        return ApiResponse.<UserCreationResponse>builder()
                .code(HttpStatus.CREATED.value())
                .data(userService.registration(request))
                .build();
    }

    @GetMapping
    ApiResponse<List<ParticipantInfoDetailResponse>> searchUser(@RequestParam String username) {
        return ApiResponse.<List<ParticipantInfoDetailResponse>>builder()
                .code(HttpStatus.OK.value())
                .data(userService.searchUser(username))
                .build();
    }

    @PostMapping("/register-fcm-token")
    ApiResponse<Void> registerFcmToken(@RequestBody RegisterFcmTokenRequest request) {
        userService.registrationFcmToken(request);
        return ApiResponse.<Void>builder()
                .code(HttpStatus.OK.value())
                .message("Successfully registered FCM token")
                .build();
    }

    @GetMapping("/info")
    ApiResponse<UserDetailResponse> getCurrentUser() {
        return ApiResponse.<UserDetailResponse>builder()
                .code(HttpStatus.OK.value())
                .data(userService.getCurrentUser())
                .build();
    }
}
