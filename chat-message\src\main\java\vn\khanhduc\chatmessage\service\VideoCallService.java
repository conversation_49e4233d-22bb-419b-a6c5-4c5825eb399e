package vn.khanhduc.chatmessage.service;

import vn.khanhduc.chatmessage.dto.video.*;
import vn.khanhduc.chatmessage.entity.User;
import vn.khanhduc.chatmessage.repository.UserRepository;
import com.twilio.rest.video.v1.Room;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@RequiredArgsConstructor
@Slf4j
public class VideoCallService {

    private final TwilioVideoService twilioVideoService;
    private final UserRepository userRepository;
    private final SimpMessagingTemplate messagingTemplate;

    // Store active rooms and participants
    private final Map<String, VideoCallRoom> activeRooms = new ConcurrentHashMap<>();

    public VideoCallRoomResponse joinRoom(String conversationId, String userId, VideoCallJoinRequest request) {
        try {
            // Create or get Twilio room
            Room twilioRoom = twilioVideoService.createOrJoinRoom(conversationId);

            // Get user info
            User user = userRepository.findById(userId)
                    .orElseThrow(() -> new RuntimeException("User not found"));

            // Create participant
            VideoCallParticipant participant = VideoCallParticipant.builder()
                    .id(userId)
                    .username(user.getUsername())
                    .isVideoEnabled(request.isVideo())
                    .isMuted(!request.isAudio())
                    .joinedAt(LocalDateTime.now())
                    .build();

            // Update active room
            VideoCallRoom room = activeRooms.computeIfAbsent(conversationId, k ->
                    VideoCallRoom.builder()
                            .roomId(conversationId)
                            .roomName(conversationId)
                            .participants(new ConcurrentHashMap<>())
                            .createdAt(LocalDateTime.now())
                            .build()
            );

            room.getParticipants().put(userId, participant);

            // Notify other participants
            notifyParticipantJoined(conversationId, participant);

            return VideoCallRoomResponse.builder()
                    .roomId(conversationId)
                    .roomName(conversationId)
                    .participants(new ArrayList<>(room.getParticipants().values()))
                    .build();

        } catch (Exception e) {
            log.error("Error joining room: {} for user: {}", conversationId, userId, e);
            throw new RuntimeException("Failed to join room", e);
        }
    }

    public void leaveRoom(String conversationId, String userId) {
        try {
            VideoCallRoom room = activeRooms.get(conversationId);
            if (room != null) {
                VideoCallParticipant participant = room.getParticipants().remove(userId);
                if (participant != null) {
                    // Notify other participants
                    notifyParticipantLeft(conversationId, participant);
                }

                // If no participants left, remove room
                if (room.getParticipants().isEmpty()) {
                    activeRooms.remove(conversationId);
                    twilioVideoService.endRoom(conversationId);
                }
            }
        } catch (Exception e) {
            log.error("Error leaving room: {} for user: {}", conversationId, userId, e);
            throw new RuntimeException("Failed to leave room", e);
        }
    }

    public VideoCallParticipantsResponse getRoomParticipants(String conversationId) {
        VideoCallRoom room = activeRooms.get(conversationId);
        List<VideoCallParticipant> participants = room != null ?
                new ArrayList<>(room.getParticipants().values()) :
                List.of();

        return VideoCallParticipantsResponse.builder()
                .participants(participants)
                .build();
    }

    public void inviteToCall(String conversationId, String inviterId, List<String> participantIds) {
        try {
            User inviter = userRepository.findById(inviterId)
                    .orElseThrow(() -> new RuntimeException("Inviter not found"));

            VideoCallInviteMessage inviteMessage = VideoCallInviteMessage.builder()
                    .conversationId(conversationId)
                    .inviterId(inviterId)
                    .inviterName(inviter.getUsername())
                    .timestamp(LocalDateTime.now())
                    .build();

            // Send invite to each participant
            for (String participantId : participantIds) {
                messagingTemplate.convertAndSendToUser(
                        participantId,
                        "/queue/video-call-invite",
                        inviteMessage
                );
            }

        } catch (Exception e) {
            log.error("Error inviting to call: {} by user: {}", conversationId, inviterId, e);
            throw new RuntimeException("Failed to invite to call", e);
        }
    }

    public void endCall(String conversationId, String userId) {
        try {
            VideoCallRoom room = activeRooms.remove(conversationId);
            if (room != null) {
                // Notify all participants that call ended
                VideoCallEndMessage endMessage = VideoCallEndMessage.builder()
                        .conversationId(conversationId)
                        .endedBy(userId)
                        .timestamp(LocalDateTime.now())
                        .build();

                for (String participantId : room.getParticipants().keySet()) {
                    messagingTemplate.convertAndSendToUser(
                            participantId,
                            "/queue/video-call-end",
                            endMessage
                    );
                }

                // End Twilio room
                twilioVideoService.endRoom(conversationId);
            }
        } catch (Exception e) {
            log.error("Error ending call: {} by user: {}", conversationId, userId, e);
            throw new RuntimeException("Failed to end call", e);
        }
    }

    private void notifyParticipantJoined(String conversationId, VideoCallParticipant participant) {
        VideoCallRoom room = activeRooms.get(conversationId);
        if (room != null) {
            VideoCallParticipantJoinedMessage message = VideoCallParticipantJoinedMessage.builder()
                    .conversationId(conversationId)
                    .participant(participant)
                    .timestamp(LocalDateTime.now())
                    .build();

            for (String participantId : room.getParticipants().keySet()) {
                if (!participantId.equals(participant.getId())) {
                    messagingTemplate.convertAndSendToUser(
                            participantId,
                            "/queue/video-call-participant-joined",
                            message
                    );
                }
            }
        }
    }

    private void notifyParticipantLeft(String conversationId, VideoCallParticipant participant) {
        VideoCallRoom room = activeRooms.get(conversationId);
        if (room != null) {
            VideoCallParticipantLeftMessage message = VideoCallParticipantLeftMessage.builder()
                    .conversationId(conversationId)
                    .participantId(participant.getId())
                    .participantName(participant.getUsername())
                    .timestamp(LocalDateTime.now())
                    .build();

            for (String participantId : room.getParticipants().keySet()) {
                messagingTemplate.convertAndSendToUser(
                        participantId,
                        "/queue/video-call-participant-left",
                        message
                );
            }
        }
    }
}