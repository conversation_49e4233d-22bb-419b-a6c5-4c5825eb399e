"use client";

import React, { useState, useEffect } from 'react';
import { VideoCameraIcon, PhoneXMarkIcon, UserIcon } from '@heroicons/react/24/outline';
import { VideoCallInvitation as VideoCallInvitationType } from '@/types/video';
import { ApiService } from '@/api/axios';
import toast from 'react-hot-toast';

interface VideoCallInvitationProps {
    invitation: VideoCallInvitationType;
    onAccept: (invitation: VideoCallInvitationType) => void;
    onDecline: (invitation: VideoCallInvitationType) => void;
    onExpire: (invitationId: string) => void;
}

export const VideoCallInvitation: React.FC<VideoCallInvitationProps> = ({
    invitation,
    onAccept,
    onDecline,
    onExpire
}) => {
    const [timeLeft, setTimeLeft] = useState<number>(0);
    const [isResponding, setIsResponding] = useState(false);

    useEffect(() => {
        // Calculate initial time left
        const expiresAt = new Date(invitation.expiresAt).getTime();
        const now = Date.now();
        const initialTimeLeft = Math.max(0, Math.floor((expiresAt - now) / 1000));
        
        setTimeLeft(initialTimeLeft);

        if (initialTimeLeft <= 0) {
            onExpire(invitation.id);
            return;
        }

        // Start countdown timer
        const timer = setInterval(() => {
            setTimeLeft(prev => {
                const newTimeLeft = prev - 1;
                if (newTimeLeft <= 0) {
                    clearInterval(timer);
                    onExpire(invitation.id);
                    return 0;
                }
                return newTimeLeft;
            });
        }, 1000);

        return () => clearInterval(timer);
    }, [invitation.expiresAt, invitation.id, onExpire]);

    const handleAccept = async () => {
        if (isResponding) return;
        
        setIsResponding(true);
        try {
            await ApiService.respondToVideoCallInvitation(invitation.id, 'accept');
            onAccept(invitation);
            toast.success('Joining video call...');
        } catch (error) {
            console.error('Failed to accept video call:', error);
            toast.error('Failed to join video call');
        } finally {
            setIsResponding(false);
        }
    };

    const handleDecline = async () => {
        if (isResponding) return;
        
        setIsResponding(true);
        try {
            await ApiService.respondToVideoCallInvitation(invitation.id, 'decline');
            onDecline(invitation);
            toast.success('Video call declined');
        } catch (error) {
            console.error('Failed to decline video call:', error);
            toast.error('Failed to decline video call');
        } finally {
            setIsResponding(false);
        }
    };

    const formatTimeLeft = (seconds: number): string => {
        const minutes = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${minutes}:${secs.toString().padStart(2, '0')}`;
    };

    if (invitation.status !== 'pending' || timeLeft <= 0) {
        return null;
    }

    return (
        <div className="fixed top-4 right-4 z-50 bg-white rounded-lg shadow-2xl border border-gray-200 p-6 max-w-sm w-full animate-slide-in-right">
            <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <VideoCameraIcon className="h-6 w-6 text-blue-600" />
                    </div>
                </div>
                
                <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                        <UserIcon className="h-4 w-4 text-gray-400" />
                        <p className="text-sm font-medium text-gray-900 truncate">
                            {invitation.inviterUsername}
                        </p>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">
                        is inviting you to a video call
                    </p>
                    
                    <div className="flex items-center justify-between mb-4">
                        <span className="text-xs text-gray-500">
                            Expires in {formatTimeLeft(timeLeft)}
                        </span>
                        <div className="w-16 h-1 bg-gray-200 rounded-full overflow-hidden">
                            <div 
                                className="h-full bg-red-500 transition-all duration-1000 ease-linear"
                                style={{ 
                                    width: `${Math.max(0, (timeLeft / 30) * 100)}%` // Assuming 30 seconds expiry
                                }}
                            />
                        </div>
                    </div>
                    
                    <div className="flex space-x-3">
                        <button
                            onClick={handleAccept}
                            disabled={isResponding}
                            className="flex-1 bg-green-500 hover:bg-green-600 disabled:bg-green-300 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                        >
                            {isResponding ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                                <>
                                    <VideoCameraIcon className="h-4 w-4" />
                                    <span>Accept</span>
                                </>
                            )}
                        </button>
                        
                        <button
                            onClick={handleDecline}
                            disabled={isResponding}
                            className="flex-1 bg-red-500 hover:bg-red-600 disabled:bg-red-300 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors flex items-center justify-center space-x-2"
                        >
                            {isResponding ? (
                                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                                <>
                                    <PhoneXMarkIcon className="h-4 w-4" />
                                    <span>Decline</span>
                                </>
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
};

// CSS for animation (add to your global CSS or Tailwind config)
const styles = `
@keyframes slide-in-right {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.animate-slide-in-right {
    animation: slide-in-right 0.3s ease-out;
}
`;
