package vn.khanhduc.chatmessage.service;

import vn.khanhduc.chatmessage.dto.S3Properties;
import vn.khanhduc.chatmessage.dto.response.FileMetaDataResponse;
import vn.khanhduc.chatmessage.utils.FileUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.core.async.AsyncRequestBody;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.services.s3.S3AsyncClient;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.PutObjectRequest;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "FILE-SERVICE")
public class S3Service {

    private final S3Client s3Client;
    private final S3AsyncClient s3AsyncClient;
    private final S3Properties s3Properties;

    public List<FileMetaDataResponse> uploadFileSync(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            log.warn("Upload files is null or empty");
            return new ArrayList<>();
        }

        List<FileMetaDataResponse> fileMetaData = new ArrayList<>();

        String bucketName = s3Properties.getBucketName();
        String region = s3Properties.getRegion();

        for (int i = 0; i < files.size(); i++) {
            MultipartFile file = files.get(i);

            validateUploadFile(file);

            String key = FileUtils.generateKeyName(file);
            try {
                PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                        .bucket(bucketName)
                        .key(key)
                        .contentType(file.getContentType())
                        .contentLength(file.getSize())
                        .build();

                RequestBody requestBody = RequestBody.fromBytes(file.getBytes());
                s3Client.putObject(putObjectRequest, requestBody);

                log.info("Successfully uploaded file: {} to bucket: {}", key, bucketName);

                String imageUrl = String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, key);
                FileMetaDataResponse metaData = FileMetaDataResponse.builder()
                        .name(file.getOriginalFilename())
                        .contentType(file.getContentType())
                        .size(file.getSize())
                        .url(imageUrl)
                        .displayOrder(i + 1)
                        .build();

                fileMetaData.add(metaData);
            } catch (IOException e) {
                log.error("Failed to upload file: {}. Error: {}", key, e.getMessage());
                throw new RuntimeException("Upload failed", e);
            }
        }
        return fileMetaData;
    }

    public CompletableFuture<List<FileMetaDataResponse>> uploadFileAsync(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            log.error("Upload files is null or empty");
            return CompletableFuture.completedFuture(new ArrayList<>());
        }

        String bucketName = s3Properties.getBucketName();
        String region = s3Properties.getRegion();

        AtomicInteger orderCounter = new AtomicInteger(0);

        List<CompletableFuture<FileMetaDataResponse>> uploadFutures = files.stream()
                .filter(file -> !file.isEmpty())
                .map(file -> {
                    validateUploadFile(file);

                    int displayOrder = orderCounter.incrementAndGet();

                    String key = FileUtils.generateKeyName(file);
                    log.info("Starting asynchronous upload of file: {} to bucket: {}", key, bucketName);

                    try {
                        PutObjectRequest putObjectRequest = PutObjectRequest.builder()
                                .bucket(bucketName)
                                .key(key)
                                .contentType(file.getContentType())
                                .contentLength(file.getSize())
                                .build();

                        AsyncRequestBody requestBody = AsyncRequestBody.fromBytes(file.getBytes());

                        return s3AsyncClient.putObject(putObjectRequest, requestBody)
                                .thenApply(response ->  {
                                    String imageUrl = String.format("https://%s.s3.%s.amazonaws.com/%s", bucketName, region, key);
                                    return FileMetaDataResponse.builder()
                                            .name(file.getOriginalFilename())
                                            .contentType(file.getContentType())
                                            .size(file.getSize())
                                            .url(imageUrl)
                                            .displayOrder(displayOrder)
                                            .build();
                                })
                                .exceptionally(throwable -> {
                                    log.error("Failed to upload file: {} to bucket: {}. Error: {}", key, bucketName, throwable.getMessage());
                                    throw new RuntimeException("Upload failed", throwable);
                                });
                    } catch (IOException e) {
                        log.error("Failed to read file bytes: {}", e.getMessage());
                        throw new RuntimeException("File reading failed", e);
                    }
                })
                .toList();

        return CompletableFuture.allOf(uploadFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> uploadFutures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .toList());
    }

    private void validateUploadFile(MultipartFile file) {
        if (file.isEmpty()) {
            log.warn("Skipping empty file: {}", file.getOriginalFilename());
            return;
        }

        if (file.getSize() > s3Properties.getMaxFileSize()) {
            log.warn("Upload file size is greater than s3 file size");
            throw new RuntimeException("Upload file size greater than s3 file size");
        }
    }

}