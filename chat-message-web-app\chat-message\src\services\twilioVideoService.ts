import { connect, Room, LocalParticipant, RemoteParticipant, LocalVideoTrack, LocalAudioTrack, RemoteVideoTrack, RemoteAudioTrack } from 'twilio-video';
import { VideoCallParticipant, VideoCallRoom, TwilioAccessToken, VideoCallSettings } from '@/types/video';
import { ApiService } from '@/api/axios';

export class TwilioVideoService {
    private room: Room | null = null;
    private localVideoTrack: LocalVideoTrack | null = null;
    private localAudioTrack: LocalAudioTrack | null = null;
    private participants: Map<string, VideoCallParticipant> = new Map();

    // Event callbacks
    private onParticipantConnected?: (participant: VideoCallParticipant) => void;
    private onParticipantDisconnected?: (participantId: string) => void;
    private onRoomConnected?: (room: VideoCallRoom) => void;
    private onRoomDisconnected?: (room: VideoCallRoom) => void;
    private onTrackSubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;
    private onTrackUnsubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;

    constructor() {
        this.setupEventHandlers();
    }

    /**
     * Initialize video calling with event callbacks
     */
    initialize(callbacks: {
        onParticipantConnected?: (participant: VideoCallParticipant) => void;
        onParticipantDisconnected?: (participantId: string) => void;
        onRoomConnected?: (room: VideoCallRoom) => void;
        onRoomDisconnected?: (room: VideoCallRoom) => void;
        onTrackSubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;
        onTrackUnsubscribed?: (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant) => void;
    }) {
        this.onParticipantConnected = callbacks.onParticipantConnected;
        this.onParticipantDisconnected = callbacks.onParticipantDisconnected;
        this.onRoomConnected = callbacks.onRoomConnected;
        this.onRoomDisconnected = callbacks.onRoomDisconnected;
        this.onTrackSubscribed = callbacks.onTrackSubscribed;
        this.onTrackUnsubscribed = callbacks.onTrackUnsubscribed;
    }

    /**
     * Join a video call room
     */
    async joinRoom(conversationId: string, settings: VideoCallSettings): Promise<VideoCallRoom> {
        try {
            // Get access token from backend
            const tokenData = await ApiService.getTwilioAccessToken(conversationId);

            // Create local tracks based on settings
            const tracks = [];

            if (settings.video) {
                this.localVideoTrack = await this.createLocalVideoTrack();
                tracks.push(this.localVideoTrack);
            }

            if (settings.audio) {
                this.localAudioTrack = await this.createLocalAudioTrack();
                tracks.push(this.localAudioTrack);
            }

            // Connect to Twilio room
            this.room = await connect(tokenData.token, {
                name: tokenData.roomName,
                tracks: tracks,
                video: settings.video,
                audio: settings.audio
            });

            // Setup room event handlers
            this.setupRoomEventHandlers();

            // Add existing participants
            this.room.participants.forEach(participant => {
                this.handleParticipantConnected(participant);
            });

            // Add local participant to participants map
            const localParticipant: VideoCallParticipant = {
                id: this.room.localParticipant.sid,
                username: this.room.localParticipant.identity,
                isLocal: true,
                isMuted: !settings.audio,
                isVideoEnabled: settings.video
            };
            this.participants.set(this.room.localParticipant.sid, localParticipant);

            const roomData: VideoCallRoom = {
                roomId: this.room.sid,
                roomName: this.room.name,
                participants: this.getParticipantsArray(),
                status: 'connected',
                startTime: new Date()
            };

            this.onRoomConnected?.(roomData);
            return roomData;

        } catch (error) {
            console.error('Failed to join room:', error);
            throw error;
        }
    }

    /**
     * Leave the current video call room
     */
    async leaveRoom(): Promise<void> {
        if (this.room) {
            this.room.disconnect();
            this.room = null;
        }

        // Clean up local tracks
        if (this.localVideoTrack) {
            this.localVideoTrack.stop();
            this.localVideoTrack = null;
        }

        if (this.localAudioTrack) {
            this.localAudioTrack.stop();
            this.localAudioTrack = null;
        }

        this.participants.clear();
    }

    /**
     * Toggle local video on/off
     */
    async toggleVideo(): Promise<boolean> {
        if (!this.room) return false;

        if (this.localVideoTrack) {
            // Disable video
            this.room.localParticipant.unpublishTrack(this.localVideoTrack);
            this.localVideoTrack.stop();
            this.localVideoTrack = null;
            return false;
        } else {
            // Enable video
            this.localVideoTrack = await this.createLocalVideoTrack();
            this.room.localParticipant.publishTrack(this.localVideoTrack);
            return true;
        }
    }

    /**
     * Toggle local audio on/off
     */
    async toggleAudio(): Promise<boolean> {
        if (!this.room) return false;

        if (this.localAudioTrack) {
            // Mute audio
            this.room.localParticipant.unpublishTrack(this.localAudioTrack);
            this.localAudioTrack.stop();
            this.localAudioTrack = null;
            return false;
        } else {
            // Unmute audio
            this.localAudioTrack = await this.createLocalAudioTrack();
            this.room.localParticipant.publishTrack(this.localAudioTrack);
            return true;
        }
    }

    /**
     * Get current room information
     */
    getCurrentRoom(): VideoCallRoom | null {
        if (!this.room) return null;

        return {
            roomId: this.room.sid,
            roomName: this.room.name,
            participants: this.getParticipantsArray(),
            status: this.room.state === 'connected' ? 'connected' : 'disconnected',
            startTime: new Date() // You might want to track this properly
        };
    }

    /**
     * Get local video track for preview
     */
    getLocalVideoTrack(): LocalVideoTrack | null {
        return this.localVideoTrack;
    }

    /**
     * Get local audio track
     */
    getLocalAudioTrack(): LocalAudioTrack | null {
        return this.localAudioTrack;
    }

    private async createLocalVideoTrack(): Promise<LocalVideoTrack> {
        const { createLocalVideoTrack } = await import('twilio-video');
        return createLocalVideoTrack({
            width: 640,
            height: 480,
            frameRate: 24
        });
    }

    private async createLocalAudioTrack(): Promise<LocalAudioTrack> {
        const { createLocalAudioTrack } = await import('twilio-video');
        return createLocalAudioTrack();
    }

    private setupRoomEventHandlers(): void {
        if (!this.room) return;

        // Handle participant connected
        this.room.on('participantConnected', this.handleParticipantConnected);

        // Handle participant disconnected
        this.room.on('participantDisconnected', this.handleParticipantDisconnected);

        // Handle room disconnected
        this.room.on('disconnected', (room) => {
            const roomData: VideoCallRoom = {
                roomId: room.sid,
                roomName: room.name,
                participants: [],
                status: 'disconnected',
                startTime: new Date()
            };
            this.onRoomDisconnected?.(roomData);
        });
    }

    private getParticipantsArray(): VideoCallParticipant[] {
        return Array.from(this.participants.values());
    }

    private handleParticipantConnected = (participant: RemoteParticipant): void => {
        const participantData: VideoCallParticipant = {
            id: participant.sid,
            username: participant.identity,
            isLocal: false,
            isMuted: !participant.audioTracks.size,
            isVideoEnabled: !!participant.videoTracks.size
        };

        this.participants.set(participant.sid, participantData);
        this.onParticipantConnected?.(participantData);

        // Subscribe to participant's tracks
        participant.tracks.forEach(publication => {
            if (publication.isSubscribed) {
                this.handleTrackSubscribed(publication.track!, participantData);
            }
        });

        participant.on('trackSubscribed', (track) => {
            this.handleTrackSubscribed(track, participantData);
        });

        participant.on('trackUnsubscribed', (track) => {
            this.handleTrackUnsubscribed(track, participantData);
        });
    };

    private handleParticipantDisconnected = (participant: RemoteParticipant): void => {
        this.participants.delete(participant.sid);
        this.onParticipantDisconnected?.(participant.sid);
    };

    private handleTrackSubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant): void => {
        this.onTrackSubscribed?.(track, participant);
    };

    private handleTrackUnsubscribed = (track: RemoteVideoTrack | RemoteAudioTrack, participant: VideoCallParticipant): void => {
        this.onTrackUnsubscribed?.(track, participant);
    };
}

// Export singleton instance
export const twilioVideoService = new TwilioVideoService();
