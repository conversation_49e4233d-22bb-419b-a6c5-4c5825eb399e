package vn.khanhduc.chatmessage.service;

import vn.khanhduc.chatmessage.dto.request.RegisterFcmTokenRequest;
import vn.khanhduc.chatmessage.dto.request.UserCreationRequest;
import vn.khanhduc.chatmessage.dto.response.ParticipantInfoDetailResponse;
import vn.khanhduc.chatmessage.dto.response.UserCreationResponse;
import vn.khanhduc.chatmessage.dto.response.UserDetailResponse;
import vn.khanhduc.chatmessage.dto.response.UserDetailResponse;
import vn.khanhduc.chatmessage.entity.User;
import vn.khanhduc.chatmessage.exception.AppException;
import vn.khanhduc.chatmessage.exception.ErrorCode;
import vn.khanhduc.chatmessage.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "USER-SERVICE")
public class UserService {

    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;

    public UserCreationResponse registration(UserCreationRequest request) {
        if(userRepository.existsByEmail(request.getEmail()))
            throw new AppException(ErrorCode.USER_EXISTED);


        User user = User.builder()
                .email(request.getEmail())
                .username(request.getUsername())
                .password(passwordEncoder.encode(request.getPassword()))
                .build();

        userRepository.save(user);

        return UserCreationResponse.builder()
                .email(user.getEmail())
                .username(user.getUsername())
                .build();
    }

    public List<ParticipantInfoDetailResponse> searchUser(String username) {
        return userRepository.findByUsernameContaining(username)
                .stream()
                .map(user -> ParticipantInfoDetailResponse.builder()
                        .userId(user.getId())
                        .username(user.getUsername())
                        .avatar(user.getAvatar())
                        .build())
                .toList();
    }

    public void registrationFcmToken(RegisterFcmTokenRequest request) {
        var userId = SecurityContextHolder.getContext().getAuthentication().getName();
        var user = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        user.setFcmToken(request.getFcmToken());
        userRepository.save(user);
        log.info("FCM token registered successfully");
    }

    public UserDetailResponse getCurrentUser() {
        var userId = SecurityContextHolder.getContext().getAuthentication().getName();
        var user = userRepository.findById(userId)
                .orElseThrow(() -> new AppException(ErrorCode.USER_NOT_FOUND));

        return UserDetailResponse.builder()
                .id(user.getId())
                .email(user.getEmail())
                .name(user.getUsername())
                .build();
    }


}
