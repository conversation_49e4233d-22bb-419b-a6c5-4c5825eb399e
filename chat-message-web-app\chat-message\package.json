{"name": "chat-message", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@stomp/stompjs": "^7.1.1", "@types/twilio-video": "^2.7.3", "@zegocloud/zego-uikit-prebuilt": "^2.15.1", "antd": "^5.26.4", "axios": "^1.10.0", "firebase": "^11.10.0", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-toastify": "^11.0.5", "sockjs-client": "^1.6.1", "twilio-video": "^2.32.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-toastify": "^4.0.2", "@types/sockjs-client": "^1.5.4", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "typescript": "^5"}}