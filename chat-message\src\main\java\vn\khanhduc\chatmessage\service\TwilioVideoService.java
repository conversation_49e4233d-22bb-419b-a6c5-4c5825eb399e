package vn.khanhduc.chatmessage.service;

import com.twilio.jwt.accesstoken.AccessToken;
import com.twilio.jwt.accesstoken.VideoGrant;
import com.twilio.rest.video.v1.Room;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class TwilioVideoService {

    @Value("${twilio.account-id}")
    private String accountSid;

    @Value("${twilio.sid}")
    private String apiKey;

    @Value("${twilio.secret}")
    private String apiSecret;

    public String generateAccessToken(String userId, String roomName) {
        try {
            VideoGrant grant = new VideoGrant();
            grant.setRoom(roomName);

            AccessToken token = new AccessToken.Builder(accountSid, apiKey, apiSecret)
                    .identity(userId)
                    .grant(grant)
                    .build();

            return token.toJwt();
        } catch (Exception e) {
            log.error("Error generating access token for user: {} in room: {}", userId, roomName, e);
            throw new RuntimeException("Failed to generate access token", e);
        }
    }

    public Room createOrJoinRoom(String roomName) {
        try {
            Room room = Room.creator()
                    .setUniqueName(roomName)
                    .setType(Room.RoomType.GROUP)
                    .setMaxParticipants(10)
                    .create();
            log.info("Created room: {}", roomName);
            return room;
        } catch (Exception e) {
            log.error("Error creating room: {}", roomName, e);
            throw new RuntimeException("Failed to create room", e);
        }
    }

    // Loại bỏ các method có vấn đề
    public void endRoom(String roomName) {
        log.info("Room {} will be automatically closed when all participants leave", roomName);
    }
}