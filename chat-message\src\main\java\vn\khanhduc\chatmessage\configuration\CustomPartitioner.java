package vn.khanhduc.chatmessage.configuration;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Partitioner;
import org.apache.kafka.common.Cluster;
import java.util.Map;

@Slf4j(topic = "CUSTOM-PARTITIONER")
public class CustomPartitioner implements Partitioner {

    @Override
    public int partition(String topic, Object key, byte[] bytes, Object o1, byte[] bytes1, Cluster cluster) {
        String sender = (String) key;

        if(sender == null) {
            return 0;
        }

        int totalPartition = cluster.partitionCountForTopic(topic);

        log.info("Total partition: {}", totalPartition);
        return Math.abs(sender.hashCode()) % totalPartition;
    }

    @Override
    public void close() {

    }

    @Override
    public void configure(Map<String, ?> map) {

    }
}
