package vn.khanhduc.chatmessage.utils;

import org.springframework.data.domain.Page;
import vn.khanhduc.chatmessage.dto.response.PageResponse;
import java.util.function.Function;

public class PaginationUtils {

    public static <T, R> PageResponse<R> paginate(
            int page,
            int size,
            Page<T> pageEntity,
            final Function<T, R> mapper) {

        return PageResponse.<R>builder()
                .currentPages(page)
                .pageSizes(size)
                .totalPages(pageEntity.getTotalPages())
                .totalElements(pageEntity.getTotalElements())
                .data(pageEntity.getContent()
                        .stream()
                        .map(mapper)
                        .toList())
                .build();
    }

}
