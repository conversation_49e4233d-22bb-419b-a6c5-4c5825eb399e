package vn.khanhduc.chatmessage.mapper;

import vn.khanhduc.chatmessage.dto.response.ConversationCreationResponse;
import vn.khanhduc.chatmessage.dto.response.ParticipantInfoDetailResponse;
import vn.khanhduc.chatmessage.entity.Conversation;
import vn.khanhduc.chatmessage.enums.ConversationType;

public class ConversationMapper {

    private ConversationMapper() {}

    public static ConversationCreationResponse mapToConversationResponse(Conversation conversation, String userId) {
        ConversationCreationResponse response = ConversationCreationResponse.builder()
                .id(conversation.getId())
                .conversationType(conversation.getConversationType())
                .participantHash(conversation.getParticipantHash())
                .participantInfo(conversation.getParticipants().stream()
                        .map(participantInfo -> ParticipantInfoDetailResponse.builder()
                                .userId(participantInfo.getUser().getId())
                                .username(participantInfo.getUser().getUsername())
                                .avatar(participantInfo.getUser().getAvatar())
                                .build())
                        .toList())
                .createdAt(conversation.getCreatedAt())
                .build();

        if(conversation.getConversationType() == ConversationType.GROUP) {
            response.setConversationName(conversation.getName());
            response.setConversationAvatar(conversation.getConversationAvatar());
        } else {
            if(conversation.getParticipants().size() == 1) {
                response.setConversationName(conversation.getParticipants().getFirst().getUser().getUsername());
                response.setConversationAvatar(conversation.getParticipants().getFirst().getUser().getAvatar());
            } else {
                conversation.getParticipants().stream()
                        .filter(participantInfo -> ! participantInfo.getUser().getId().equals(userId))
                        .findFirst().ifPresent(participantInfo -> {
                            response.setConversationName(participantInfo.getUser().getUsername());
                            response.setConversationAvatar(participantInfo.getUser().getAvatar());
                        });
            }
        }

        return response;
    }
}
