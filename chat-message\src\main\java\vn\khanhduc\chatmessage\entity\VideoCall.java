package vn.khanhduc.chatmessage.entity;

import jakarta.persistence.*;
import lombok.*;
import vn.khanhduc.chatmessage.enums.CallStatus;
import vn.khanhduc.chatmessage.enums.CallType;
import java.time.LocalDateTime;

@Entity
@Table(name = "video_calls")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Builder
public class VideoCall {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private String id;

    @Column(name = "conversation_id", nullable = false)
    private String conversationId;

    @Column(name = "caller_id", nullable = false)
    private String callerId;

    @Column(name = "receiver_id")
    private String receiverId;

    @Column(name = "zego_room_id", nullable = false)
    private String zegoRoomId;

    @Enumerated(EnumType.STRING)
    @Column(name = "call_type", nullable = false)
    private CallType callType; // VIDEO, AUDIO

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private CallStatus status; // INITIATED, RINGING, ACCEPTED, REJECTED, ENDED, MISSED

    @Column(name = "started_at")
    private LocalDateTime startedAt;

    @Column(name = "ended_at")
    private LocalDateTime endedAt;

    @Column(name = "duration_seconds")
    private Integer durationSeconds;

    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
}