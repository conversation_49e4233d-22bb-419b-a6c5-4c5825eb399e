package vn.khanhduc.chatmessage.dto.response;

import vn.khanhduc.chatmessage.enums.CallStatus;
import vn.khanhduc.chatmessage.enums.CallType;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class VideoCallInviteMessage {
    private String callId;
    private String conversationId;
    private String callerId;
    private String callerName;
    private String callerAvatar;
    private String receiverId;
    private CallType callType;
    private CallStatus status;
    private String zegoRoomId;
}
