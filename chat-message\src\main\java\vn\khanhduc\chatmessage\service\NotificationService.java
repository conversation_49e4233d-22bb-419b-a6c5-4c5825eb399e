package vn.khanhduc.chatmessage.service;

import com.google.firebase.messaging.FirebaseMessaging;
import com.google.firebase.messaging.FirebaseMessagingException;
import com.google.firebase.messaging.Message;
import com.google.firebase.messaging.Notification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import vn.khanhduc.chatmessage.entity.User;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j(topic = "NOTIFICATION-SERVICE")
public class NotificationService {

    private final FirebaseMessaging firebaseMessaging;

    public void sendNotification(List<User> users, String sender, String content) {
        if(users == null || users.isEmpty()) {
            return;
        }
        users.forEach((user) -> {
            if(user.getFcmToken() != null) {
                String messageBody = content;
                if (messageBody.length() > 80) {
                    messageBody = messageBody.substring(0, 77) + "...";
                }
                Message message = Message.builder()
                        .setNotification(Notification.builder()
                                .setTitle(sender)
                                .setBody(messageBody)
                                .build())
                        .setToken(user.getFcmToken())
                        .putData("icon", "https://cdn-icons-png.flaticon.com/512/8943/8943377.png")
                        .build();
                try {
                    String response = firebaseMessaging.send(message);
                    log.info("Sent message: {}", response);
                } catch (FirebaseMessagingException e) {
                    throw new RuntimeException(e);
                }
            }
        });
    }

}
