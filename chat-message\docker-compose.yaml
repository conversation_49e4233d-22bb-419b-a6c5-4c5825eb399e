services:
  kafka:
    image: confluentinc/cp-kafka:7.5.5
    container_name: kafka-confluentinc
    hostname: kafka
    ports:
      - "9092:9092"
      - "9094:9094"
    environment:
      - KAFKA_NODE_ID=1
      - KAFKA_PROCESS_ROLES=broker,controller
      - K<PERSON><PERSON>_CONTROLLER_QUORUM_VOTERS=1@kafka:9093
      - KAFKA_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,PLAINTEXT_HOST://:9094
      - <PERSON><PERSON><PERSON>_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:9094
      - <PERSON><PERSON><PERSON>_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      - KAFKA_CONTROLLER_LISTENER_NAMES=CONTROLLER
      - KAFKA_LOG_DIRS=/var/lib/kafka/data
      - CLUSTER_ID=MkU3OEVBNTcwNTJENDM2Qk
      - KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR=1
      - KAFKA_TRANSACTION_STATE_LOG_MIN_ISR=1
      - KAFKA_NUM_PARTITIONS=3
    volumes:
      - kafka-data:/var/lib/kafka/data
    networks:
      - app_network

  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: kafka-ui
    ports:
      - "7777:8080"
    environment:
      - KAFKA_CLUSTERS_0_NAME=local
      - KAFKA_CLUSTERS_0_BOOTSTRAP_SERVERS=kafka:9092
      - KAFKA_CLUSTERS_0_ZOOKEEPER=
      - DYNAMIC_CONFIG_ENABLED=true
    depends_on:
      - kafka
    networks:
      - app_network

  redis:
    image: redis:7
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - app_network

volumes:
  kafka-data:
  redis-data:

networks:
  app_network:
    driver: bridge
