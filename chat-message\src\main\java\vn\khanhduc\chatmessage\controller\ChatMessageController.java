package vn.khanhduc.chatmessage.controller;

import vn.khanhduc.chatmessage.dto.request.ChatRequest;
import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.response.ChatResponse;
import vn.khanhduc.chatmessage.dto.response.PageResponse;
import vn.khanhduc.chatmessage.service.ChatMessageService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/chat")
public class ChatMessageController {

    private final ChatMessageService chatMessageService;

    @PostMapping
    ApiResponse<ChatResponse> creatMessage(@RequestBody ChatRequest request) {
        String principal = SecurityContextHolder.getContext().getAuthentication().getName();
        request.setSender(principal);

        return ApiResponse.<ChatResponse>builder()
                .code(HttpStatus.CREATED.value())
                .data(chatMessageService.createMessage(request))
                .build();
    }

    @GetMapping("/{conversationId}")
    ApiResponse<PageResponse<ChatResponse>> getMessageByConversationId(
            @RequestParam(required = false, defaultValue = "1") int page,
            @RequestParam(required = false, defaultValue = "15") int size,
            @PathVariable String conversationId) {

        return ApiResponse.<PageResponse<ChatResponse>>builder()
                .code(HttpStatus.OK.value())
                .data(chatMessageService.getMessageByConversationId(page, size, conversationId))
                .build();
    }

}
