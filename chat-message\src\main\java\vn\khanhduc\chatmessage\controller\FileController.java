package vn.khanhduc.chatmessage.controller;

import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.response.FileMetaDataResponse;
import vn.khanhduc.chatmessage.service.S3Service;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.request.async.DeferredResult;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/files")
public class FileController {

    private final S3Service s3Service;

    @PostMapping("/upload-media-sync")
    ApiResponse<List<FileMetaDataResponse>> uploadMediaSync(@RequestParam List<MultipartFile> files) {
        return ApiResponse.<List<FileMetaDataResponse>>builder()
                .code(HttpStatus.OK.value())
                .data(s3Service.uploadFileSync(files))
                .build();
    }

    @PostMapping("/upload-media-async")
    public DeferredResult<ApiResponse<List<FileMetaDataResponse>>> uploadMediaASync(@RequestParam("files") List<MultipartFile> files) {
        DeferredResult<ApiResponse<List<FileMetaDataResponse>>> deferredResult = new DeferredResult<>();

        s3Service.uploadFileAsync(files)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        deferredResult.setErrorResult(ApiResponse.<List<String>>builder()
                                .code(HttpStatus.INTERNAL_SERVER_ERROR.value())
                                .message("Upload failed: " + throwable.getMessage())
                                .build());
                    } else {
                        deferredResult.setResult(ApiResponse.<List<FileMetaDataResponse>>builder()
                                .code(HttpStatus.OK.value())
                                .data(result)
                                .build());
                    }
                });

        return deferredResult;
    }

}
