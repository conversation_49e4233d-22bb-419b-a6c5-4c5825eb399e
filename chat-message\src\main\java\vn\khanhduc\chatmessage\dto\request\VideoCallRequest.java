package vn.khanhduc.chatmessage.dto.request;

import vn.khanhduc.chatmessage.enums.CallType;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class VideoCallRequest {

    @NotBlank(message = "Conversation ID cannot be blank")
    private String conversationId;

    @NotNull(message = "Call type cannot be null")
    private CallType callType;

    private String receiverId; // Optional for group calls
}
