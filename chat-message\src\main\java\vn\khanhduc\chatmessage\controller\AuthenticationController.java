package vn.khanhduc.chatmessage.controller;

import vn.khanhduc.chatmessage.dto.request.SignInRequest;
import vn.khanhduc.chatmessage.dto.response.ApiResponse;
import vn.khanhduc.chatmessage.dto.response.SignInResponse;
import vn.khanhduc.chatmessage.service.AuthenticationService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/auth")
public class AuthenticationController {

    private final AuthenticationService authenticationService;

    @PostMapping("/sign-in")
    ApiResponse<SignInResponse> signIn(@RequestBody SignInRequest request) {
        return ApiResponse.<SignInResponse>builder()
                .code(HttpStatus.OK.value())
                .data(authenticationService.signIn(request))
                .build();
    }

}
