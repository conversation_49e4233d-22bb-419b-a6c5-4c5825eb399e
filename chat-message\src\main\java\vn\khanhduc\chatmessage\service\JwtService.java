package vn.khanhduc.chatmessage.service;

import com.nimbusds.jose.*;
import com.nimbusds.jose.crypto.MACSigner;
import com.nimbusds.jwt.JWTClaimsSet;
import vn.khanhduc.chatmessage.entity.User;
import vn.khanhduc.chatmessage.enums.TokenType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.UUID;

@Service
@Slf4j(topic = "JWT-SERVICE")
public class JwtService {

    @Value("${jwt.secret-key}")
    private String secretKey;

    public String generateToken(User user, TokenType type) {
        JWSAlgorithm algorithm = type == TokenType.ACCESS_TOKEN ? JWSAlgorithm.HS384 : JWSAlgorithm.HS512;
        JWSHeader header = new JWSHeader(algorithm);

        long expired = type == TokenType.ACCESS_TOKEN ? 60 : 14;
        Date expiredTime = new Date(Instant.now().plus(expired, type == TokenType.ACCESS_TOKEN ?
                ChronoUnit.MINUTES : ChronoUnit.DAYS).toEpochMilli());

        JWTClaimsSet claimsSet = new JWTClaimsSet.Builder()
                .subject(user.getId())
                .issueTime(new Date())
                .expirationTime(expiredTime)
                .jwtID(UUID.randomUUID().toString())
                .build();

        Payload payload = new Payload(claimsSet.toJSONObject());

        JWSObject jwsObject = new JWSObject(header, payload);

        try {
            jwsObject.sign(new MACSigner(secretKey));
            return jwsObject.serialize();
        } catch (JOSEException e) {
            throw new RuntimeException(e);
        }
    }
}
