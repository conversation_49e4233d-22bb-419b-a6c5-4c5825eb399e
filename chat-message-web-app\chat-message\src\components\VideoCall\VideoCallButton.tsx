"use client";

import React, { useState } from 'react';
import { VideoCameraIcon } from '@heroicons/react/24/outline';
import { ApiService } from '@/api/axios';
import { ConversationCreationResponse } from '@/types/chat';
import toast from 'react-hot-toast';

interface VideoCallButtonProps {
    conversation: ConversationCreationResponse;
    onStartCall: (conversationId: string) => void;
    disabled?: boolean;
    className?: string;
}

export const VideoCallButton: React.FC<VideoCallButtonProps> = ({
    conversation,
    onStartCall,
    disabled = false,
    className = ""
}) => {
    const [isStarting, setIsStarting] = useState(false);

    const handleStartCall = async () => {
        if (isStarting || disabled) return;

        setIsStarting(true);
        try {
            // Get participant IDs from conversation
            const participantIds = conversation.participants
                ?.map(p => p.id)
                .filter(id => id !== undefined) || [];

            if (participantIds.length === 0) {
                toast.error('No participants found for video call');
                return;
            }

            // Create video call invitation
            await ApiService.createVideoCallInvitation(conversation.id, participantIds);
            
            // Start the call
            onStartCall(conversation.id);
            
            toast.success('Starting video call...');
        } catch (error) {
            console.error('Failed to start video call:', error);
            toast.error('Failed to start video call');
        } finally {
            setIsStarting(false);
        }
    };

    return (
        <button
            onClick={handleStartCall}
            disabled={disabled || isStarting}
            className={`
                inline-flex items-center justify-center p-2 rounded-lg transition-all duration-200
                ${disabled || isStarting
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-50 text-blue-600 hover:bg-blue-100 hover:text-blue-700 active:bg-blue-200'
                }
                ${className}
            `}
            title="Start video call"
        >
            {isStarting ? (
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
            ) : (
                <VideoCameraIcon className="h-5 w-5" />
            )}
        </button>
    );
};
